# Database Development Guide

## Table of Contents
- [Database Architecture](#database-architecture)
- [Entity Framework Configuration](#entity-framework-configuration)
- [Database Contexts](#database-contexts)
- [Entity Models](#entity-models)
- [Repository Pattern](#repository-pattern)
- [Migrations & Schema Management](#migrations--schema-management)
- [Data Access Patterns](#data-access-patterns)
- [Performance Considerations](#performance-considerations)

## Database Architecture

**Note:** This project uses MariaDB 10.11+ as its primary database system. The Pomelo.EntityFrameworkCore.MySql provider is used for MariaDB connectivity and is fully compatible with MariaDB while maintaining MySQL compatibility.

The application uses a multi-database architecture with MariaDB as the primary database system:

```
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
├─────────────────────────────────────────────────────────────┤
│                Entity Framework Core                       │
├─────────────────────────────────────────────────────────────┤
│  MyAdaptiveCloud  │  Agent DB  │  Logs DB  │  Billing DB   │
│     Context       │  Context   │  Context  │   Context     │
├─────────────────────────────────────────────────────────────┤
│  myadaptivecloud  │  acagent   │  logs     │  Services     │
│    (MariaDB)      │ (MariaDB)  │ (MariaDB) │  (SQL Server) │
└─────────────────────────────────────────────────────────────┘
```

### Database Purposes
- **myadaptivecloud**: Main application data (users, organizations, configurations)
- **acagent**: Agent management and device monitoring data
- **myadaptivecloudlogs**: Application logging and audit trails
- **Services**: Billing and financial data (SQL Server)

## Entity Framework Configuration

### Connection String Configuration
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=myacdb01.dsm1.ippathways.net;User Id=myac;Database=myadaptivecloud;Connection Timeout=1000000",
    "ACAgentConnection": "Server=myacdb01.dsm1.ippathways.net;User Id=myac_user;Database=acagent;",
    "LogsConnection": "Server=myacdb01.dsm1.ippathways.net;User Id=myac;Database=myadaptivecloudlogs;Connection Timeout=1000000",
    "BillingConnection": "Server=sql01.dsm1.ippathways.net;User Id=svc-myac;Database=Services;TrustServerCertificate=true;"
  }
}
```

### DbContext Registration
```csharp
// Main application context (MariaDB)
services.AddDbContext<MyAdaptiveCloudContext>((provider, options) =>
{
    options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString))
        .AddInterceptors(provider.GetRequiredService<CustomSaveChangesInterceptor>())
        .EnableSensitiveDataLogging(isDevelopmentEnvironment);
});

// Agent context (MariaDB)
services.AddDbContext<AgentContext>(options =>
    options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString))
        .EnableSensitiveDataLogging(isDevelopmentEnvironment));

// Billing context (SQL Server)
services.AddDbContext<BillingContext>(options =>
    options.UseSqlServer(connectionString)
        .EnableSensitiveDataLogging(isDevelopmentEnvironment));
```

### Database Providers
- **Pomelo.EntityFrameworkCore.MySql**: MariaDB provider (also compatible with MySQL)
- **Microsoft.EntityFrameworkCore.SqlServer**: SQL Server provider for billing

## Database Contexts

### MyAdaptiveCloudContext (Main Database)
```csharp
public class MyAdaptiveCloudContext : DbContext
{
    public MyAdaptiveCloudContext(DbContextOptions<MyAdaptiveCloudContext> options) : base(options) { }

    // Core entities
    public DbSet<Organization> Organization { get; set; }
    public DbSet<User> User { get; set; }
    public DbSet<UserRole> UserRole { get; set; }
    public DbSet<Permission> Permission { get; set; }
    
    // Device management
    public DbSet<DeviceFolder> DeviceFolder { get; set; }
    public DbSet<DeviceControl> DeviceControl { get; set; }
    public DbSet<AlertRule> AlertRule { get; set; }
    
    // Cloud infrastructure
    public DbSet<AcCwVmMap> AcCwVmMap { get; set; }
    public DbSet<AcCwAccountMap> AcCwAccountMap { get; set; }
    
    // Notifications
    public DbSet<Notification> Notification { get; set; }
    public DbSet<NotificationRecipient> NotificationRecipient { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Configure entity relationships and constraints
        ConfigureOrganizationMappings(modelBuilder);
        ConfigureUserRelationships(modelBuilder);
        ConfigureDeviceEntities(modelBuilder);
    }
}
```

### AgentContext (Agent Database)
```csharp
public class AgentContext : DbContext
{
    public AgentContext(DbContextOptions<AgentContext> options) : base(options) { }

    public DbSet<Agent> Agent { get; set; }
    public DbSet<ActiveAgent> ActiveAgent { get; set; }
    public DbSet<DeletedAgent> DeletedAgent { get; set; }
    public DbSet<ActivationKey> ActivationKey { get; set; }
    public DbSet<AgentDeviceFolder> AgentDeviceFolder { get; set; }
    public DbSet<AgentRemoteDesktopConnection> AgentRemoteDesktopConnection { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Agent inheritance hierarchy
        modelBuilder.Entity<Agent>().HasDiscriminator(a => a.Status)
            .HasValue<ActiveAgent>(AgentStatus.Active)
            .HasValue<DeletedAgent>(AgentStatus.Deleted)
            .HasValue<MarkedForDeletionAgent>(AgentStatus.MarkedForDeletion);
    }
}
```

## Entity Models

### Core Entity Example
```csharp
public class Organization
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string CompanyShortName { get; set; }
    public bool IsActive { get; set; }
    public int? ParentOrganizationId { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime? ModifiedDate { get; set; }

    // Navigation properties
    public virtual Organization ParentOrganization { get; set; }
    public virtual ICollection<Organization> ChildOrganizations { get; set; }
    public virtual ICollection<User> Users { get; set; }
    public virtual ICollection<UserRole> UserRoles { get; set; }
}
```

### Entity Configuration
```csharp
public static void ConfigureOrganizationMappings(ModelBuilder modelBuilder)
{
    modelBuilder.Entity<OrganizationMapping>()
        .HasQueryFilter(p => p.Organization.IsActive)
        .HasDiscriminator<string>("Application")
        .HasValue<BillingDBOrganizationMapping>(Applications.BillingDB.ToString())
        .HasValue<CloudInfraOrganizationMapping>(Applications.CloudInfra.ToString())
        .HasValue<ConnectWiseOrganizationMapping>(Applications.ConnectWise.ToString())
        .HasValue<DataProtectionOrganizationMapping>(Applications.DataProtection.ToString());
}
```

### Entity Relationships
- **One-to-Many**: Organization → Users, Organization → Child Organizations
- **Many-to-Many**: Users ↔ Roles (through UserRole junction table)
- **Inheritance**: Agent hierarchy using Table-Per-Hierarchy (TPH)
- **Global Query Filters**: Automatic filtering of inactive records

## Repository Pattern

### Repository Interface
```csharp
public interface IOrganizationRepository
{
    Task<Organization> GetByIdAsync(int id);
    Task<List<Organization>> GetAllAsync();
    Task<List<Organization>> GetByParentIdAsync(int parentId);
    Task<Organization> CreateAsync(Organization entity);
    Task UpdateAsync(Organization entity);
    Task DeleteAsync(int id);
    Task<bool> ExistsAsync(int id);
}
```

### Repository Implementation
```csharp
public class OrganizationRepository : IOrganizationRepository
{
    private readonly MyAdaptiveCloudContext _context;

    public OrganizationRepository(MyAdaptiveCloudContext context)
    {
        _context = context;
    }

    public async Task<Organization> GetByIdAsync(int id)
    {
        return await _context.Organization
            .Include(o => o.ParentOrganization)
            .Include(o => o.ChildOrganizations)
            .FirstOrDefaultAsync(o => o.Id == id);
    }

    public async Task<List<Organization>> GetByParentIdAsync(int parentId)
    {
        return await _context.Organization
            .Where(o => o.ParentOrganizationId == parentId)
            .OrderBy(o => o.Name)
            .ToListAsync();
    }

    public async Task<Organization> CreateAsync(Organization entity)
    {
        entity.CreatedDate = DateTime.UtcNow;
        _context.Organization.Add(entity);
        await _context.SaveChangesAsync();
        return entity;
    }
}
```

### Repository Registration
```csharp
public static IServiceCollection AddScopedRepositories(this IServiceCollection services)
{
    services.AddScoped<IOrganizationRepository, OrganizationRepository>();
    services.AddScoped<IUserRepository, UserRepository>();
    services.AddScoped<IDeviceFolderRepository, DeviceFolderRepository>();
    services.AddScoped<IAlertRuleRepository, AlertRuleRepository>();
    // ... more repositories
    return services;
}
```

## Migrations & Schema Management

### Liquibase Integration
The project uses Liquibase for database schema management instead of EF Core migrations:

```yaml
# changelog.yaml
databaseChangeLog:
    - include:
        file: "v1.4changelog.sql"
        context: "main"
    
    - include:
        file: "StoredProcs.sql"
        context: "main"
    
    - include:
        file: "SeedData.sql"
        context: "dev, test"
    
    - include:
        file: "MYAC-383.sql"
        context: "main"
```

### Docker Liquibase Setup
```yaml
# docker-compose.yaml
myacdbliquibase:
    image: liquibase/liquibase:4.17
    command: '/liquibase/liquibase --search-path=/liquibase/changelog/ --defaults-file=/run/secrets/liquibase-properties update --contexts="test,main"'
    volumes:
        - type: bind
          source: publish/sql/myadaptivecloud
          target: /liquibase/changelog
    depends_on:
        myacdb:
            condition: service_healthy
```

### Schema Management Benefits
- **Version Control**: SQL scripts in source control
- **Environment Consistency**: Same scripts across all environments
- **Rollback Support**: Structured rollback capabilities
- **Team Collaboration**: Clear change tracking

## Data Access Patterns

### Query Patterns
```csharp
// Simple query
var organizations = await _context.Organization
    .Where(o => o.IsActive)
    .ToListAsync();

// Complex query with joins
var userRoles = await _context.UserRole
    .Include(ur => ur.User)
    .Include(ur => ur.Role)
    .Include(ur => ur.Organization)
    .Where(ur => ur.UserId == userId)
    .ToListAsync();

// Projection for performance
var organizationSummary = await _context.Organization
    .Select(o => new OrganizationSummaryDTO
    {
        Id = o.Id,
        Name = o.Name,
        UserCount = o.Users.Count()
    })
    .ToListAsync();
```

### Interceptors
```csharp
public class CustomSaveChangesInterceptor : SaveChangesInterceptor
{
    public override InterceptionResult<int> SavingChanges(DbContextEventData eventData, InterceptionResult<int> result)
    {
        UpdateTimestamps(eventData.Context);
        return base.SavingChanges(eventData, result);
    }

    private static void UpdateTimestamps(DbContext context)
    {
        var entries = context.ChangeTracker.Entries()
            .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

        foreach (var entry in entries)
        {
            if (entry.State == EntityState.Added && entry.Property("CreatedDate").CurrentValue == null)
            {
                entry.Property("CreatedDate").CurrentValue = DateTime.UtcNow;
            }
            
            if (entry.State == EntityState.Modified)
            {
                entry.Property("ModifiedDate").CurrentValue = DateTime.UtcNow;
            }
        }
    }
}
```

## Performance Considerations

### Query Optimization
- **Include Strategy**: Use Include() for related data
- **Projection**: Select only needed columns
- **Pagination**: Implement Skip/Take for large datasets
- **Async Operations**: Use async methods for I/O operations

### Connection Management
- **Connection Pooling**: Configured automatically by EF Core
- **DbContext Lifetime**: Scoped lifetime in DI container
- **Read-Only Contexts**: Separate contexts for read operations

### Caching Strategies
- **Memory Cache**: For frequently accessed reference data
- **Query Result Caching**: Cache expensive query results
- **Entity Tracking**: Use NoTracking for read-only scenarios
