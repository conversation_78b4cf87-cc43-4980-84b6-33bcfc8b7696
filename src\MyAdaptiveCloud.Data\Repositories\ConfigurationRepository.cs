using Microsoft.EntityFrameworkCore;
using MyAdaptiveCloud.Data.MyAdaptiveCloud;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Contexts;

namespace MyAdaptiveCloud.Data.Repositories
{
    public class ConfigurationRepository : IConfigurationRepository
    {
        private readonly MyAdaptiveCloudContext _dbContext;

        public ConfigurationRepository(MyAdaptiveCloudContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<List<ConfigurationValueItem>> GetConfigurationByCategoryAndOrganization(string categoryName, int organizationId)
        {
            return await _dbContext.GetConfigurationValuesPerOrganization(organizationId, categoryName);
        }

        public async Task<List<ConfigurationValueItem>> GetByOrganization(int organizationId, bool? isUserVisibleOnly = null)
        {
            return await _dbContext.GetConfigurationValuesPerOrganization(organizationId, null, isUserVisibleOnly);
        }

        public async Task<List<ConfigurationValueOrganization>> GetValuesByOrganizationAndIds(int organizationId, IEnumerable<int> ids)
        {
            return await _dbContext.ConfigurationValueOrganization
                .Where(configurationValue =>
                    ids.Contains(configurationValue.ConfigurationValuesId) && configurationValue.OrganizationId == organizationId).ToListAsync();
        }

        public async Task SaveChanges()
        {
            await _dbContext.SaveChangesAsync();
        }
    }
}